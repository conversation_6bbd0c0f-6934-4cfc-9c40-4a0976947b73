-- ox_target/server/admin.lua

ESX = exports["Framework"]:getSharedObject()

-- Fonction pour vérifier si un joueur est admin
local function isPlayerAdmin(source)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return false end
    
    -- Vérifiez selon votre système d'admin
    -- Exemple si vous utilisez un système de groupe :
    return xPlayer.getGroup() == 'admin' or xPlayer.getGroup() == 'fondateur'
    
    -- Ou si vous utilisez un autre système, adaptez ici
end

-- Event pour synchroniser le grade admin avec le client
RegisterServerEvent('ox_target:requestAdminGrade')
AddEventHandler('ox_target:requestAdminGrade', function()
    local source = source
    local grade = 'user' -- Par défaut
    
    if isPlayerAdmin(source) then
        local xPlayer = ESX.GetPlayerFromId(source)
        if xPlayer then
            local group = xPlayer.getGroup()
            if group == 'admin' or group == 'fondateur' then
                grade = group
            end
        end
    end
    
    TriggerClientEvent('ox_target:setAdminGrade', source, grade)
end)

-- Event pour supprimer un véhicule (visible par tous)
RegisterServerEvent('ox_target:admin_deleteVehicle')
AddEventHandler('ox_target:admin_deleteVehicle', function(netId)
    local source = source
    
    if not isPlayerAdmin(source) then
        --print(('[ox_target] Joueur %s a tenté de supprimer un véhicule sans permission'):format(source))
        return
    end
    
    local vehicle = NetworkGetEntityFromNetworkId(netId)
    if DoesEntityExist(vehicle) then
        DeleteEntity(vehicle)
        
        -- Log l'action
        local xPlayer = ESX.GetPlayerFromId(source)
        if xPlayer then
            --print(('[ox_target] Admin %s (%s) a supprimé le véhicule %s'):format(xPlayer.getName(), source, netId))
        end
    end
end)

-- Event pour réparer un véhicule (visible par tous)
RegisterServerEvent('ox_target:admin_repairVehicle')
AddEventHandler('ox_target:admin_repairVehicle', function(netId)
    local source = source
    
    if not isPlayerAdmin(source) then
        --print(('[ox_target] Joueur %s a tenté de réparer un véhicule sans permission'):format(source))
        return
    end
    
    -- Déclenche la réparation sur tous les clients
    TriggerClientEvent('ox_target:repairVehicleForAll', -1, netId)
    
    -- Log l'action
    local xPlayer = ESX.GetPlayerFromId(source)
    if xPlayer then
        --print(('[ox_target] Admin %s (%s) a réparé le véhicule %s'):format(xPlayer.getName(), source, netId))
    end
end)

-- Event pour nettoyer un véhicule (visible par tous)
RegisterServerEvent('ox_target:admin_cleanVehicle')
AddEventHandler('ox_target:admin_cleanVehicle', function(netId)
    local source = source
    
    if not isPlayerAdmin(source) then
        --print(('[ox_target] Joueur %s a tenté de nettoyer un véhicule sans permission'):format(source))
        return
    end
    
    -- Déclenche le nettoyage sur tous les clients
    TriggerClientEvent('ox_target:cleanVehicleForAll', -1, netId)
end)

-- Event pour faire le plein (visible par tous)
RegisterServerEvent('ox_target:admin_fuelVehicle')
AddEventHandler('ox_target:admin_fuelVehicle', function(netId)
    local source = source
    
    if not isPlayerAdmin(source) then
        --print(('[ox_target] Joueur %s a tenté de faire le plein sans permission'):format(source))
        return
    end
    
    -- Déclenche le plein sur tous les clients
    TriggerClientEvent('ox_target:fuelVehicleForAll', -1, netId)
end)

-- Event pour retourner un véhicule (visible par tous)
RegisterServerEvent('ox_target:admin_flipVehicle')
AddEventHandler('ox_target:admin_flipVehicle', function(netId)
    local source = source
    
    if not isPlayerAdmin(source) then
        --print(('[ox_target] Joueur %s a tenté de retourner un véhicule sans permission'):format(source))
        return
    end
    
    -- Déclenche le retournement sur tous les clients
    TriggerClientEvent('ox_target:flipVehicleForAll', -1, netId)
end)

-- Event pour customiser un véhicule (visible par tous)
RegisterServerEvent('ox_target:admin_customVehicle')
AddEventHandler('ox_target:admin_customVehicle', function(netId)
    local source = source
    
    if not isPlayerAdmin(source) then
        --print(('[ox_target] Joueur %s a tenté de customiser un véhicule sans permission'):format(source))
        return
    end
    
    -- Déclenche la customisation sur tous les clients
    TriggerClientEvent('ox_target:customVehicleForAll', -1, netId)
end)

-- Event pour peindre un véhicule (visible par tous)
RegisterServerEvent('ox_target:admin_paintVehicle')
AddEventHandler('ox_target:admin_paintVehicle', function(netId, paintType, colorType)
    local source = source
    
    if not isPlayerAdmin(source) then
        --print(('[ox_target] Joueur %s a tenté de peindre un véhicule sans permission'):format(source))
        return
    end
    
    -- Déclenche la peinture sur tous les clients
    TriggerClientEvent('ox_target:paintVehicleForAll', -1, netId, paintType, colorType)
    
    -- Log l'action
    local xPlayer = ESX.GetPlayerFromId(source)
    if xPlayer then
        --print(('[ox_target] Admin %s (%s) a peint le véhicule %s (%s) en %s'):format(xPlayer.getName(), source, netId, paintType, colorType))
    end
end)

-- Event déclenché quand un joueur se connecte
RegisterServerEvent('esx:playerLoaded')
AddEventHandler('esx:playerLoaded', function(source)
    -- Envoie le grade admin au client
    Citizen.Wait(1000) -- Petit délai pour s'assurer que tout est chargé
    TriggerEvent('ox_target:requestAdminGrade')
end)


/*RegisterNetEvent('ox_target:admin_deleteProp')
AddEventHandler('ox_target:admin_deleteProp', function(netId)
    local source = source
    if not isPlayerAdmin(source) then
        print(('[ox_target] Joueur %s a tenté de supprimer un prop sans permission'):format(source))
        return
    end
    local entity = NetworkGetEntityFromNetworkId(netId)
    if entity and DoesEntityExist(entity) then
        -- Forcer le contrôle réseau avant suppression
        if not NetworkHasControlOfEntity(entity) then
            NetworkRequestControlOfEntity(entity)
            local tries = 0
            while not NetworkHasControlOfEntity(entity) and tries < 20 do
                Citizen.Wait(50)
                NetworkRequestControlOfEntity(entity)
                tries = tries + 1
            end
        end
        DeleteEntity(entity)
        local xPlayer = ESX.GetPlayerFromId(source)
        if xPlayer then
            print(('[ox_target] Admin %s (%s) a supprimé le prop %s'):format(xPlayer.getName(), source, netId))
        end
    else
        print(('[ox_target] Impossible de supprimer le prop %s (inexistant)'):format(netId))
    end
end)*/

-- Troll menu admin :

RegisterServerEvent('ox_target:adminFirePlayer')
AddEventHandler('ox_target:adminFirePlayer', function(targetId)
    local source = source
    if not isPlayerAdmin(source) then return end
    TriggerClientEvent('ox_target:adminFirePlayer', targetId)
end)

RegisterServerEvent('ox_target:adminLaunchPlayer')
AddEventHandler('ox_target:adminLaunchPlayer', function(targetId)
    local source = source
    if not isPlayerAdmin(source) then return end
    TriggerClientEvent('ox_target:adminLaunchPlayer', targetId)
end)

RegisterServerEvent('ox_target:adminRagdollPlayer')
AddEventHandler('ox_target:adminRagdollPlayer', function(targetId)
    local source = source
    if not isPlayerAdmin(source) then return end
    TriggerClientEvent('ox_target:adminRagdollPlayer', targetId)
end)

RegisterServerEvent('ox_target:adminDrunkPlayer')
AddEventHandler('ox_target:adminDrunkPlayer', function(targetId)
    local source = source
    if not isPlayerAdmin(source) then return end
    TriggerClientEvent('ox_target:adminDrunkPlayer', targetId)
end)

-- Admin revive player
RegisterServerEvent('ox_target:adminRevivePlayer')
AddEventHandler('ox_target:adminRevivePlayer', function(targetId)
    local source = source
    if not isPlayerAdmin(source) then return end
    TriggerClientEvent('ox_target:adminRevivePlayer', targetId)
end)

-- Admin heal player
RegisterServerEvent('ox_target:adminHealPlayer')
AddEventHandler('ox_target:adminHealPlayer', function(targetId)
    local source = source
    if not isPlayerAdmin(source) then return end
    TriggerClientEvent('ox_target:adminHealPlayer', targetId)
end)