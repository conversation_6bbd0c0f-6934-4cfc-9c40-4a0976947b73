lib.versionCheck('overextended/ox_target')

if not lib.checkDependency('ox_lib', '3.30.0', true) then return end

ESX = exports["Framework"]:getSharedObject()

---@type table<number, EntityInterface>
local entityStates = {}

---@param netId number
RegisterNetEvent('ox_target:setEntityHasOptions', function(netId)
    local entity = Entity(NetworkGetEntityFromNetworkId(netId))
    entity.state.hasTargetOptions = true
    entityStates[netId] = entity
end)

---@param netId number
---@param door number
RegisterNetEvent('ox_target:toggleEntityDoor', function(netId, door)
    local entity = NetworkGetEntityFromNetworkId(netId)
    if not DoesEntityExist(entity) then return end

    local owner = NetworkGetEntityOwner(entity)
    TriggerClientEvent('ox_target:toggleEntityDoor', owner, netId, door)
end)

CreateThread(function()
    local arr = {}
    local num = 0

    while true do
        Wait(10000)

        for netId, entity in pairs(entityStates) do
            if not DoesEntityExist(entity.__data) or not entity.state.hasTargetOptions then
                entityStates[netId] = nil
                num += 1

                arr[num] = netId
            end
        end

        if num > 0 then
            TriggerClientEvent('ox_target:removeEntity', -1, arr)
            table.wipe(arr)

            num = 0
        end
    end
end)

-- Synchronisation du grade admin/fondateur à la connexion et sur demande
RegisterNetEvent('ox_target:syncAdminGrade', function()
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)
    if xPlayer then
        TriggerClientEvent('ox_target:setAdminGrade', src, xPlayer.getGroup())
    end
end)

AddEventHandler('esx:playerLoaded', function(source, xPlayer)
    TriggerClientEvent('ox_target:setAdminGrade', source, xPlayer.getGroup())
end)

RegisterNetEvent('iZeyy:police:deleteNetworkedProp')
AddEventHandler('iZeyy:police:deleteNetworkedProp', function(netId)
    local object = NetworkGetEntityFromNetworkId(netId)
    if object and DoesEntityExist(object) then
        DeleteEntity(object)
    end
    -- Envoie à tous les clients pour supprimer le prop
    TriggerClientEvent('iZeyy:police:clientDeleteProp', -1, netId)
end)

RegisterNetEvent('ox_target:police_duplicate_prop')
AddEventHandler('ox_target:police_duplicate_prop', function(model, offset, heading)
    local src = source
    TriggerClientEvent('ox_target:police_duplicate_prop', src, model, offset, heading)
end)

